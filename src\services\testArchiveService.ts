import { Timestamp } from 'firebase/firestore';
import { Plant, DiagnosticRecord, GeminiDiagnosis } from '../types';
import { archiveService, ArchiveData } from './archiveService';

/**
 * Service temporaire pour créer des archives fictives de test
 * À utiliser uniquement pour le débogage et les tests
 */
class TestArchiveService {
  
  /**
   * Crée une archive fictive pour tester le système
   */
  async createTestArchive(userId: string, year: number = 2023): Promise<string> {
    console.log(`🧪 Création d'une archive fictive pour l'année ${year}...`);
    
    try {
      // Générer des plantes fictives
      const testPlants = this.generateTestPlants(userId);
      
      // Générer des diagnostics fictifs
      const testDiagnostics = this.generateTestDiagnostics(userId, testPlants, year);
      
      // Créer l'archive fictive
      const archiveData: ArchiveData = {
        year,
        userId,
        plants: testPlants,
        diagnostics: testDiagnostics,
        archivedAt: Timestamp.now(),
        totalPlants: testPlants.length,
        totalDiagnostics: testDiagnostics.length,
        geminiAccessible: true
      };
      
      // Sauvegarder via le service d'archive normal
      await archiveService.createTestArchive(userId, year, archiveData);
      
      console.log(`✅ Archive fictive créée: ${testPlants.length} plantes, ${testDiagnostics.length} diagnostics`);
      return `Archive fictive ${year} créée avec succès`;
      
    } catch (error) {
      console.error('❌ Erreur lors de la création de l\'archive fictive:', error);
      throw error;
    }
  }
  
  /**
   * Génère des plantes fictives pour les tests
   */
  private generateTestPlants(userId: string): Plant[] {
    const plantNames = [
      'Monstera Deliciosa Test',
      'Pothos Doré Test', 
      'Ficus Benjamina Test',
      'Sansevieria Test',
      'Philodendron Test'
    ];
    
    return plantNames.map((name, index) => ({
      id: `test-plant-${index + 1}`,
      userId,
      name,
      species: `Espèce test ${index + 1}`,
      location: `Salon test ${index + 1}`,
      imageUrl: '',
      notes: `Notes de test pour ${name}`,
      createdAt: Timestamp.fromDate(new Date(2023, index, 15)),
      lastWatered: Timestamp.fromDate(new Date(2023, index + 1, 1)),
      nextWateringDate: Timestamp.fromDate(new Date(2023, index + 1, 8)),
      isArchived: false
    }));
  }
  
  /**
   * Génère des diagnostics fictifs pour les tests
   */
  private generateTestDiagnostics(userId: string, plants: Plant[], year: number): DiagnosticRecord[] {
    const diagnostics: DiagnosticRecord[] = [];
    
    const diseases = [
      'Pourriture des racines',
      'Pucerons',
      'Jaunissement des feuilles',
      'Manque de lumière',
      'Excès d\'arrosage'
    ];
    
    plants.forEach((plant, plantIndex) => {
      // Créer 3-5 diagnostics par plante
      const numDiagnostics = 3 + Math.floor(Math.random() * 3);
      
      for (let i = 0; i < numDiagnostics; i++) {
        const isHealthy = Math.random() > 0.4; // 60% de chance d'être malade
        const disease = diseases[Math.floor(Math.random() * diseases.length)];
        
        const diagnosis: GeminiDiagnosis = {
          isHealthy,
          disease: isHealthy ? 'En Bonne Santé' : disease,
          confidence: 0.8 + Math.random() * 0.2,
          description: isHealthy 
            ? 'La plante est en excellente santé'
            : `Problème détecté: ${disease}`,
          recommendations: isHealthy
            ? ['Continuer les soins actuels']
            : [`Traiter contre ${disease}`, 'Ajuster l\'arrosage'],
          issues: isHealthy ? [] : [disease],
          treatmentPlan: {
            steps: isHealthy 
              ? ['Maintenir les soins actuels']
              : [`Appliquer traitement pour ${disease}`, 'Surveiller l\'évolution'],
            treatmentFrequencyDays: isHealthy ? 14 : 7,
            estimatedRecoveryDays: isHealthy ? 0 : 10 + Math.floor(Math.random() * 10)
          }
        };
        
        diagnostics.push({
          id: `test-diagnostic-${plantIndex}-${i}`,
          plantId: plant.id,
          userId,
          timestamp: Timestamp.fromDate(new Date(year, plantIndex + i, 10 + i * 5)),
          imageUrls: [`test-image-${plantIndex}-${i}.jpg`],
          diagnosis,
          nextTreatmentDate: isHealthy 
            ? undefined 
            : Timestamp.fromDate(new Date(year, plantIndex + i, 17 + i * 5))
        });
      }
    });
    
    return diagnostics;
  }
  
  /**
   * Supprime une archive de test
   */
  async deleteTestArchive(userId: string, year: number): Promise<void> {
    console.log(`🗑️ Suppression de l'archive fictive ${year}...`);
    
    try {
      await archiveService.deleteArchive(userId, year);
      console.log(`✅ Archive fictive ${year} supprimée`);
    } catch (error) {
      console.error('❌ Erreur lors de la suppression de l\'archive fictive:', error);
      throw error;
    }
  }
  
  /**
   * Vérifie si une archive est une archive de test
   */
  isTestArchive(archive: ArchiveData): boolean {
    return archive.plants.some(plant => plant.name.includes('Test')) ||
           archive.diagnostics.some(diag => diag.id.includes('test-'));
  }
}

// Instance singleton du service de test
export const testArchiveService = new TestArchiveService();
