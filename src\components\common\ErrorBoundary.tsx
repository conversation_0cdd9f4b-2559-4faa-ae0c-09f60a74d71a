/**
 * Error Boundary pour capturer et gérer les erreurs critiques de l'application
 * 
 * Fonctionnalités :
 * - Capture des erreurs React non gérées
 * - Interface de fallback utilisateur
 * - Reporting automatique des erreurs
 * - Possibilité de récupération
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { logger } from '@/utils/logger';
import { Button } from '@/components/common/Button';
import { LeafIcon } from '@/components/common/icons';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Met à jour l'état pour afficher l'interface de fallback
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log l'erreur de manière critique
    logger.critical('Erreur critique capturée par Error Boundary', {
      component: 'ErrorBoundary',
      action: 'ErrorCaught'
    }, {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      errorInfo: {
        componentStack: errorInfo.componentStack
      },
      errorId: this.state.errorId,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    });

    // Mettre à jour l'état avec les informations d'erreur
    this.setState({
      error,
      errorInfo
    });
  }

  handleReload = () => {
    logger.info('Tentative de rechargement après erreur', {
      component: 'ErrorBoundary',
      action: 'Reload'
    }, { errorId: this.state.errorId });

    // Recharger la page complètement
    window.location.reload();
  };

  handleReset = () => {
    logger.info('Tentative de récupération après erreur', {
      component: 'ErrorBoundary',
      action: 'Reset'
    }, { errorId: this.state.errorId });

    // Réinitialiser l'état de l'Error Boundary
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Interface de fallback personnalisée
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Interface de fallback par défaut
      return (
        <div className="min-h-screen flex items-center justify-center bg-[#100f1c] p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-md text-center"
          >
            {/* Icône d'erreur */}
            <div className="inline-block p-4 bg-gradient-to-r from-red-500 to-red-600 rounded-full mb-6">
              <LeafIcon className="w-16 h-16 text-white" />
            </div>

            {/* Titre */}
            <h1 className="text-3xl font-bold text-white mb-4">
              Oups ! Une erreur s'est produite
            </h1>

            {/* Description */}
            <p className="text-lg text-gray-300 mb-6">
              FloraSynth a rencontré un problème inattendu. 
              Nos équipes ont été automatiquement notifiées.
            </p>

            {/* Informations de débogage (développement uniquement) */}
            {import.meta.env.DEV && this.state.error && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="mb-6 p-4 bg-gray-800/50 rounded-lg text-left text-sm text-gray-400 overflow-scroll max-h-40"
                style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
              >
                <p className="font-semibold text-red-400 mb-2">
                  Erreur (développement) :
                </p>
                <p className="mb-2">
                  <strong>Message :</strong> {this.state.error.message}
                </p>
                <p className="mb-2">
                  <strong>ID :</strong> {this.state.errorId}
                </p>
                {this.state.error.stack && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-blue-400">
                      Stack trace
                    </summary>
                    <pre className="mt-2 text-xs whitespace-pre-wrap">
                      {this.state.error.stack}
                    </pre>
                  </details>
                )}
              </motion.div>
            )}

            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={this.handleReset}
                className="bg-[#d385f5] hover:bg-[#c374e4] text-white"
              >
                Réessayer
              </Button>
              <Button
                onClick={this.handleReload}
                className="bg-gray-600 hover:bg-gray-700 text-white"
              >
                Recharger la page
              </Button>
            </div>

            {/* Informations de contact */}
            <p className="text-sm text-gray-500 mt-6">
              Si le problème persiste, contactez le support avec l'ID : 
              <br />
              <code className="bg-gray-800 px-2 py-1 rounded text-xs">
                {this.state.errorId}
              </code>
            </p>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook pour déclencher manuellement une erreur (utile pour les tests)
 */
export const useErrorHandler = () => {
  return (error: Error, context?: string) => {
    logger.error('Erreur déclenchée manuellement', {
      component: context || 'Unknown',
      action: 'ManualError'
    }, error);

    // Relancer l'erreur pour qu'elle soit capturée par l'Error Boundary
    throw error;
  };
};

/**
 * Composant Error Boundary spécialisé pour l'authentification
 */
export const AuthErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => {
  const authFallback = (
    <div className="min-h-screen flex items-center justify-center bg-[#100f1c] p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="w-full max-w-md text-center"
      >
        <div className="inline-block p-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mb-6">
          <LeafIcon className="w-16 h-16 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-white mb-4">
          Problème d'authentification
        </h1>
        <p className="text-lg text-gray-300 mb-6">
          Un problème est survenu avec votre connexion. 
          Veuillez recharger la page et réessayer.
        </p>
        <Button
          onClick={() => window.location.reload()}
          className="bg-[#d385f5] hover:bg-[#c374e4] text-white"
        >
          Recharger et réessayer
        </Button>
      </motion.div>
    </div>
  );

  return (
    <ErrorBoundary fallback={authFallback}>
      {children}
    </ErrorBoundary>
  );
};

export default ErrorBoundary;
