import React, { useState } from 'react';
import { Bell, Calendar, AlertTriangle } from 'lucide-react';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem
} from '../../ui/dropdown-menu';
import { useNotifications, usePendingEvents } from '../../../hooks/useNotifications';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';

/**
 * Badge de notification pour la barre de navigation
 */
export const NotificationBadge: React.FC = () => {
  const navigate = useNavigate();
  const { notifications, unreadCount, markAsRead } = useNotifications();
  const { todayEvents, overdueEvents } = usePendingEvents();
  const [isOpen, setIsOpen] = useState(false);

  const totalAlerts = unreadCount + overdueEvents.length;
  const recentNotifications = notifications.slice(0, 5);

  const handleNotificationClick = async (notificationId: string) => {
    if (notifications.find(n => n.id === notificationId && !n.read)) {
      await markAsRead(notificationId);
    }
    setIsOpen(false);
    navigate('/notifications');
  };

  const handleViewAll = () => {
    setIsOpen(false);
    navigate('/notifications');
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    // Force le blur du trigger quand on ferme
    if (!open) {
      setTimeout(() => {
        const activeElement = document.activeElement as HTMLElement;
        if (activeElement && activeElement.blur) {
          activeElement.blur();
        }
      }, 0);
    }
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={handleOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="relative focus:outline-none focus:ring-0 focus:bg-transparent hover:bg-white/10 data-[state=open]:bg-white/10"
        >
          <Bell className="h-5 w-5" />
          {totalAlerts > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {totalAlerts > 9 ? '9+' : totalAlerts}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          {totalAlerts > 0 && (
            <Badge variant="secondary">{totalAlerts}</Badge>
          )}
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />

        {/* Événements urgents */}
        {overdueEvents.length > 0 && (
          <>
            <div className="px-2 py-1">
              <div className="flex items-center gap-2 text-sm font-medium text-red-600">
                <AlertTriangle className="h-4 w-4" />
                Événements en retard ({overdueEvents.length})
              </div>
            </div>
            {overdueEvents.slice(0, 2).map((event) => (
              <DropdownMenuItem
                key={event.id}
                className="flex flex-col items-start p-3 cursor-pointer"
                onClick={() => handleViewAll()}
              >
                <div className="font-medium text-red-700">{event.title}</div>
                <div className="text-sm text-gray-600">{event.plantName}</div>
                <div className="text-xs text-red-500">
                  Prévu le {event.nextActionDate.toDate().toLocaleDateString('fr-FR')}
                </div>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
          </>
        )}

        {/* Événements d'aujourd'hui */}
        {todayEvents.length > 0 && (
          <>
            <div className="px-2 py-1">
              <div className="flex items-center gap-2 text-sm font-medium text-blue-600">
                <Calendar className="h-4 w-4" />
                Aujourd'hui ({todayEvents.length})
              </div>
            </div>
            {todayEvents.slice(0, 2).map((event) => (
              <DropdownMenuItem
                key={event.id}
                className="flex flex-col items-start p-3 cursor-pointer"
                onClick={() => handleViewAll()}
              >
                <div className="font-medium text-blue-700">{event.title}</div>
                <div className="text-sm text-gray-600">{event.plantName}</div>
                <div className="text-xs text-blue-500">{event.nextActionType}</div>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
          </>
        )}

        {/* Notifications récentes */}
        {recentNotifications.length > 0 ? (
          <>
            <div className="px-2 py-1">
              <div className="text-sm font-medium text-gray-700">Notifications récentes</div>
            </div>
            {recentNotifications.map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                className="flex flex-col items-start p-3 cursor-pointer"
                onClick={() => handleNotificationClick(notification.id)}
              >
                <div className="flex items-center gap-2 w-full">
                  <div className="font-medium text-gray-900 flex-1">{notification.title}</div>
                  {!notification.read && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  )}
                </div>
                <div className="text-sm text-gray-600 line-clamp-2">{notification.message}</div>
                <div className="text-xs text-gray-500">
                  {formatDistanceToNow(notification.createdAt.toDate(), { addSuffix: true, locale: fr })}
                </div>
              </DropdownMenuItem>
            ))}
          </>
        ) : (
          <div className="px-3 py-6 text-center text-gray-500">
            <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Aucune notification</p>
          </div>
        )}

        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={handleViewAll} className="text-center justify-center font-medium">
          Voir toutes les notifications
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
