import React from 'react';
import { Button } from '@/components/common/Button';
import { 
    ArchiveBoxIcon, 
    DocumentDuplicateIcon, 
    ArrowsRightLeftIcon, 
    TrashIcon,
    CheckIcon,
    XMarkIcon
} from '@/components/common/icons';

interface PlantToolbarProps {
    selectedPlants: string[];
    totalPlants: number;
    isSelectionMode: boolean;
    onToggleSelectionMode: () => void;
    onSelectAll: () => void;
    onDeselectAll: () => void;
    onArchive: (plantIds: string[]) => void;
    onDuplicate: (plantIds: string[]) => void;
    onMove: (plantIds: string[]) => void;
    onDelete: (plantIds: string[]) => void;
}

const PlantToolbar: React.FC<PlantToolbarProps> = ({
    selectedPlants,
    totalPlants,
    isSelectionMode,
    onToggleSelectionMode,
    onSelectAll,
    onDeselectAll,
    onArchive,
    onDuplicate,
    onMove,
    onDelete
}) => {
    const selectedCount = selectedPlants.length;
    const allSelected = selectedCount === totalPlants && totalPlants > 0;

    return (
        <div className="mb-6">
            {/* Barre d'outils principale */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 bg-[#1c1a31]/80 backdrop-blur-lg rounded-2xl border border-[#3D3B5E]">
                
                {/* Section gauche : Mode sélection */}
                <div className="flex items-center gap-3">
                    <Button
                        onClick={onToggleSelectionMode}
                        variant={isSelectionMode ? "primary" : "secondary"}
                        className="flex items-center gap-2"
                    >
                        <CheckIcon className="w-4 h-4" />
                        {isSelectionMode ? 'Quitter sélection' : 'Sélectionner'}
                    </Button>

                    {isSelectionMode && (
                        <>
                            <span className="text-[#E0E0E0] text-sm">
                                {selectedCount} / {totalPlants} sélectionnée{selectedCount > 1 ? 's' : ''}
                            </span>
                            
                            <Button
                                onClick={allSelected ? onDeselectAll : onSelectAll}
                                variant="secondary"
                                className="text-xs px-3 py-1"
                            >
                                {allSelected ? 'Tout désélectionner' : 'Tout sélectionner'}
                            </Button>
                        </>
                    )}
                </div>

                {/* Section droite : Actions */}
                {isSelectionMode && selectedCount > 0 && (
                    <div className="flex flex-wrap gap-2">
                        <Button
                            onClick={() => onArchive(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-2 text-[#d385f5] border-[#d385f5] hover:bg-[#d385f5]/10"
                        >
                            <ArchiveBoxIcon className="w-4 h-4" />
                            Archiver ({selectedCount})
                        </Button>

                        <Button
                            onClick={() => onDuplicate(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-2 text-blue-400 border-blue-400 hover:bg-blue-400/10"
                        >
                            <DocumentDuplicateIcon className="w-4 h-4" />
                            Copier ({selectedCount})
                        </Button>

                        <Button
                            onClick={() => onMove(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-2 text-yellow-400 border-yellow-400 hover:bg-yellow-400/10"
                        >
                            <ArrowsRightLeftIcon className="w-4 h-4" />
                            Déplacer ({selectedCount})
                        </Button>

                        <Button
                            onClick={() => onDelete(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-2 text-red-400 border-red-400 hover:bg-red-400/10"
                        >
                            <TrashIcon className="w-4 h-4" />
                            Supprimer ({selectedCount})
                        </Button>
                    </div>
                )}
            </div>

            {/* Message d'aide en mode sélection */}
            {isSelectionMode && selectedCount === 0 && (
                <div className="mt-3 p-3 bg-[#2a2847]/50 rounded-lg border border-[#3D3B5E]">
                    <p className="text-[#E0E0E0] text-sm text-center">
                        💡 Cliquez sur les plantes pour les sélectionner, puis choisissez une action
                    </p>
                </div>
            )}
        </div>
    );
};

export default PlantToolbar;
