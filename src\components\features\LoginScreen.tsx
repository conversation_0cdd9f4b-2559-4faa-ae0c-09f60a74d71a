
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { signInWithGoogle } from '@/services/api';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/common/Button';
import { LeafIcon } from '@/components/common/icons';
import { logger } from '@/utils/logger';
import { BackgroundWrapper } from '@/components/common/BackgroundWrapper';
import { getBackgroundConfig } from '@/utils/backgroundImages';

const LoginScreen: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  // Redirection automatique si l'utilisateur est déjà connecté
  useEffect(() => {
    if (!loading && user) {
      logger.info('Redirection automatique - utilisateur déjà connecté', {
        component: 'LoginScreen',
        action: 'AutoRedirect'
      });
      navigate('/', { replace: true });
    }
  }, [user, loading, navigate]);

  const handleGoogleSignIn = async () => {
    logger.debug('Clic sur le bouton de connexion Google', {
      component: 'LoginScreen',
      action: 'LoginButtonClick'
    });

    setIsLoading(true);
    setError(null);

    try {
      const result = await signInWithGoogle();

      if (result?.success) {
        logger.info('Connexion réussie, attente de la mise à jour du contexte', {
          component: 'LoginScreen',
          action: 'LoginSuccess'
        });

        // 🎯 CORRECTION CRITIQUE : Plus de setTimeout hack !
        // La redirection sera gérée automatiquement par l'useEffect
        // quand l'AuthContext se mettra à jour via onAuthStateChanged
        // Ceci élimine les race conditions et rend le système robuste

      } else if (result?.error) {
        logger.warn('Erreur de connexion', {
          component: 'LoginScreen',
          action: 'LoginError'
        }, { error: result.error });
        setError(result.error);
      }
    } catch (err) {
      logger.error('Erreur inattendue lors de la connexion', {
        component: 'LoginScreen',
        action: 'LoginUnexpectedError'
      }, err);
      setError("Une erreur inattendue s'est produite. Veuillez réessayer.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <BackgroundWrapper backgroundKey="login" overlayOpacity={0.4} enableScroll={false} hasHeader={false}>
      <div className="flex items-center justify-center h-full p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md text-center"
        >
        <div className="relative inline-block mb-6">
          {/* Effet de halo lumineux en arrière-plan */}
          <div className="absolute inset-0 bg-gradient-to-r from-pink-400/30 via-purple-400/40 to-pink-300/30 rounded-full blur-xl scale-150 animate-pulse"></div>

          {/* Conteneur principal du logo avec dégradés multiples */}
          <div className="relative p-6 bg-gradient-to-br from-pink-200/20 via-white/30 to-purple-200/20 rounded-full shadow-2xl border border-white/20 backdrop-blur-sm">
            {/* Effet de brillance interne */}
            <div className="absolute inset-2 bg-gradient-to-tr from-white/40 via-transparent to-transparent rounded-full"></div>

            {/* Logo principal */}
            <div className="relative z-10 p-3 bg-gradient-to-br from-pink-300/80 via-purple-300/80 to-pink-400/80 rounded-full shadow-lg">
              <img
                src={getBackgroundConfig('logo').url}
                alt="FloraSynth Logo"
                className="w-16 h-16 object-contain drop-shadow-lg filter brightness-110 contrast-110"
                onError={(e) => {
                  // Fallback vers l'icône LeafIcon si l'image ne charge pas
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <LeafIcon className="w-16 h-16 text-white hidden drop-shadow-lg" />
            </div>

            {/* Effet de reflet en haut */}
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-white/30 rounded-full blur-sm"></div>
          </div>
        </div>
        <h1 className="text-5xl font-bold text-white mb-2 drop-shadow-lg">FloraSynth</h1>
        <p className="text-lg text-[#E0E0E0] mb-8 drop-shadow-md">Votre Assistant IA Personnel pour le Soin des Plantes</p>

        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 text-sm"
          >
            {error}
          </motion.div>
        )}

          <Button
            onClick={handleGoogleSignIn}
            className="w-full max-w-xs mx-auto relative overflow-hidden bg-gradient-to-r from-pink-400 via-purple-500 to-pink-500 hover:from-pink-500 hover:via-purple-600 hover:to-pink-600 text-white font-semibold py-4 px-8 rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-white/20 backdrop-blur-sm"
            disabled={isLoading}
          >
            {/* Effet de brillance animé */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 transform translate-x-[-100%] hover:translate-x-[100%] transition-transform duration-700"></div>

            {/* Contenu du bouton */}
            <span className="relative z-10 flex items-center justify-center gap-2">
              {isLoading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Connexion en cours...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Se connecter avec Google
                </>
              )}
            </span>
          </Button>
        </motion.div>
      </div>
    </BackgroundWrapper>
  );
};

export default LoginScreen;
