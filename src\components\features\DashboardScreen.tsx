import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { getPlants, addPlant, getDiagnosticRecords } from '@/services/api';
import { Plant, DiagnosticRecord } from '@/types';
import { Button } from '@/components/common/Button';
import { Card } from '@/components/common/Card';
import { Spinner } from '@/components/common/Spinner';
import {
    PlusIcon,
    LeafIcon,
    CheckIcon,
    HeartIcon,
    ExclamationTriangleIcon,
    ArchiveBoxIcon,
    DocumentDuplicateIcon,
    ArrowsRightLeftIcon,
    TrashIcon
} from '@/components/common/icons';
import { getBackgroundConfig } from '@/utils/backgroundImages';
import { BackgroundWrapper } from '@/components/common/BackgroundWrapper';

export type FilterType = 'all' | 'healthy' | 'needCare' | 'archived';

import { motion, AnimatePresence } from 'framer-motion';
import { serverTimestamp } from 'firebase/firestore';

const AddPlantModal = ({ isOpen, onClose, onAdd }: { isOpen: boolean, onClose: () => void, onAdd: (name: string, species: string, description?: string) => void }) => {
    const [name, setName] = useState('');
    const [species, setSpecies] = useState('');
    const [description, setDescription] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        await onAdd(name, species, description);
        setIsLoading(false);
        setName('');
        setSpecies('');
        setDescription('');
        onClose();
    };

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/70 flex items-center justify-center z-50"
                onClick={onClose}
            >
                <motion.div
                    initial={{ scale: 0.9, y: -20 }}
                    animate={{ scale: 1, y: 0 }}
                    exit={{ scale: 0.9, y: -20 }}
                    className="bg-[#1c1a31] p-8 rounded-2xl w-full max-w-md"
                    onClick={(e) => e.stopPropagation()}
                >
                    <h2 className="text-2xl font-bold text-white mb-6">Ajouter une Nouvelle Plante</h2>
                    <form onSubmit={handleSubmit}>
                        <div className="mb-4">
                            <label htmlFor="plant-name" className="block text-sm font-medium text-[#E0E0E0] mb-2">Nom de la Plante</label>
                            <input
                                type="text"
                                id="plant-name"
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                                required
                                className="w-full bg-[#100f1c] border border-gray-600 rounded-lg p-3 text-white focus:ring-2 focus:ring-[#a364f7] focus:outline-none"
                                placeholder="ex: Monstera Deliciosa"
                            />
                        </div>
                        <div className="mb-4">
                            <label htmlFor="plant-species" className="block text-sm font-medium text-[#E0E0E0] mb-2">Espèce (Optionnel)</label>
                            <input
                                type="text"
                                id="plant-species"
                                value={species}
                                onChange={(e) => setSpecies(e.target.value)}
                                className="w-full bg-[#100f1c] border border-gray-600 rounded-lg p-3 text-white focus:ring-2 focus:ring-[#a364f7] focus:outline-none"
                                placeholder="ex: Araceae"
                            />
                        </div>
                        <div className="mb-6">
                            <label htmlFor="plant-description" className="block text-sm font-medium text-[#E0E0E0] mb-2">Description de l'état actuel</label>
                            <textarea
                                id="plant-description"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                className="w-full bg-[#100f1c] border border-gray-600 rounded-lg p-3 text-white focus:ring-2 focus:ring-[#a364f7] focus:outline-none h-24 resize-none"
                                placeholder="Décrivez ce que vous observez sur votre plante : couleur des feuilles, état général, problèmes éventuels, etc."
                            />
                            <p className="text-xs text-gray-400 mt-1">
                                💡 Cette description aidera l'IA à mieux analyser votre plante lors des futurs diagnostics
                            </p>
                        </div>
                        <div className="flex justify-end gap-4">
                            <Button type="button" variant="secondary" onClick={onClose}>Annuler</Button>
                            <Button type="submit" isLoading={isLoading}>Ajouter la Plante</Button>
                        </div>
                    </form>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};


const DashboardScreen: React.FC = () => {
    const { user } = useAuth();
    const navigate = useNavigate();
    const [plants, setPlants] = useState<Plant[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [plantThumbnails, setPlantThumbnails] = useState<{ [plantId: string]: string }>({});

    // États pour la gestion de la sélection
    const [isSelectionMode, setIsSelectionMode] = useState(false);
    const [selectedPlants, setSelectedPlants] = useState<string[]>([]);

    // État pour les filtres
    const [activeFilter, setActiveFilter] = useState<FilterType>('all');

    useEffect(() => {
        if (user) {
            const unsubscribe = getPlants(user.uid, (fetchedPlants) => {
                setPlants(fetchedPlants);
                setIsLoading(false);

                // Récupérer les vignettes pour chaque plante
                fetchedPlants.forEach(plant => {
                    getDiagnosticRecords(user.uid, plant.id, (diagnostics) => {
                        if (diagnostics.length > 0 && diagnostics[0].imageUrls.length > 0) {
                            setPlantThumbnails(prev => ({
                                ...prev,
                                [plant.id]: diagnostics[0].imageUrls[0]
                            }));
                        }
                    });
                });
            });
            return () => unsubscribe();
        }
    }, [user]);

    const handleAddPlant = async (name: string, species: string, description?: string) => {
        if (user) {
            await addPlant(user.uid, {
                name,
                species,
                description: description || '',
                createdAt: serverTimestamp()
            });
        }
    };

    // Fonctions de gestion de la sélection
    const handleToggleSelectionMode = () => {
        setIsSelectionMode(!isSelectionMode);
        setSelectedPlants([]); // Réinitialiser la sélection
    };

    const handleSelectAll = () => {
        setSelectedPlants(plants.map(plant => plant.id));
    };

    const handleDeselectAll = () => {
        setSelectedPlants([]);
    };

    const handlePlantClick = (plantId: string) => {
        if (isSelectionMode) {
            setSelectedPlants(prev =>
                prev.includes(plantId)
                    ? prev.filter(id => id !== plantId)
                    : [...prev, plantId]
            );
        } else {
            navigate(`/plant/${plantId}`);
        }
    };

    // Actions de la barre d'outils (implémentation basique pour l'instant)
    const handleArchive = (plantIds: string[]) => {
        console.log('Archiver les plantes:', plantIds);
        // TODO: Implémenter l'archivage
    };

    const handleDuplicate = (plantIds: string[]) => {
        console.log('Dupliquer les plantes:', plantIds);
        // TODO: Implémenter la duplication
    };

    const handleMove = (plantIds: string[]) => {
        console.log('Déplacer les plantes:', plantIds);
        // TODO: Implémenter le déplacement
    };

    const handleDelete = (plantIds: string[]) => {
        console.log('Supprimer les plantes:', plantIds);
        // TODO: Implémenter la suppression en lot
    };

    // Fonctions de filtrage et comptage
    const getPlantHealthStatus = (plant: Plant): 'healthy' | 'needCare' | 'archived' => {
        // Pour l'instant, logique basique basée sur la date de création
        // TODO: Implémenter une vraie logique basée sur les diagnostics
        const daysSinceCreation = plant.createdAt
            ? Math.floor((Date.now() - plant.createdAt.toMillis()) / (1000 * 60 * 60 * 24))
            : 0;

        if (plant.archived) return 'archived';
        if (daysSinceCreation > 30) return 'needCare'; // Plante ancienne = besoin de soins
        return 'healthy';
    };

    const getFilteredPlants = () => {
        return plants.filter(plant => {
            const status = getPlantHealthStatus(plant);

            switch (activeFilter) {
                case 'healthy':
                    return status === 'healthy';
                case 'needCare':
                    return status === 'needCare';
                case 'archived':
                    return status === 'archived';
                case 'all':
                default:
                    return !plant.archived; // Toutes sauf archivées par défaut
            }
        });
    };

    const getFilterCounts = () => {
        const healthy = plants.filter(p => getPlantHealthStatus(p) === 'healthy').length;
        const needCare = plants.filter(p => getPlantHealthStatus(p) === 'needCare').length;
        const archived = plants.filter(p => getPlantHealthStatus(p) === 'archived').length;
        const all = plants.filter(p => !p.archived).length; // Toutes les non-archivées

        return { all, healthy, needCare, archived };
    };

    const filteredPlants = getFilteredPlants();
    const filterCounts = getFilterCounts();

    if (isLoading) {
        return <div className="flex items-center justify-center h-screen"><Spinner size="lg" /></div>;
    }
    
    return (
        <BackgroundWrapper backgroundKey="dashboard" overlayOpacity={0.3}>
            <div className="p-4 sm:p-8 pt-20">
                <h1 className="text-4xl font-bold text-white mb-8 drop-shadow-lg">Mon Jardin</h1>

                {/* Boutons de gestion - Ligne 1: Sélectionner */}
                <div className="mb-4 flex items-center gap-3">
                    <Button
                        onClick={handleToggleSelectionMode}
                        variant={isSelectionMode ? "primary" : "secondary"}
                        className="flex items-center gap-2"
                    >
                        <CheckIcon className="w-4 h-4" />
                        {isSelectionMode ? 'Quitter sélection' : 'Sélectionner'}
                    </Button>

                    {isSelectionMode && (
                        <>
                            <span className="text-[#E0E0E0] text-sm">
                                {selectedPlants.length} / {filteredPlants.length} sélectionnée{selectedPlants.length > 1 ? 's' : ''}
                            </span>

                            <Button
                                onClick={selectedPlants.length === filteredPlants.length ? handleDeselectAll : handleSelectAll}
                                variant="secondary"
                                className="text-xs px-3 py-1"
                            >
                                {selectedPlants.length === filteredPlants.length ? 'Tout désélectionner' : 'Tout sélectionner'}
                            </Button>
                        </>
                    )}
                </div>

                {/* Ligne 2: Filtres */}
                <div className="mb-4 flex flex-wrap gap-3">
                    <Button
                        onClick={() => setActiveFilter('all')}
                        variant={activeFilter === 'all' ? "primary" : "secondary"}
                        className={`flex items-center gap-2 ${
                            activeFilter === 'all'
                                ? 'bg-[#d385f5] text-white border-[#d385f5]'
                                : 'text-[#d385f5] border-[#d385f5] hover:bg-[#d385f5]/10'
                        }`}
                    >
                        <LeafIcon className="w-4 h-4" />
                        Toutes
                        <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                            activeFilter === 'all' ? 'bg-white/20 text-white' : 'bg-[#d385f5]/10 text-[#d385f5]'
                        }`}>
                            {filterCounts.all}
                        </span>
                    </Button>

                    <Button
                        onClick={() => setActiveFilter('healthy')}
                        variant={activeFilter === 'healthy' ? "primary" : "secondary"}
                        className={`flex items-center gap-2 ${
                            activeFilter === 'healthy'
                                ? 'bg-green-500 text-white border-green-500'
                                : 'text-green-400 border-green-400 hover:bg-green-400/10'
                        }`}
                    >
                        <HeartIcon className="w-4 h-4" />
                        En bonne santé
                        <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                            activeFilter === 'healthy' ? 'bg-white/20 text-white' : 'bg-green-400/10 text-green-400'
                        }`}>
                            {filterCounts.healthy}
                        </span>
                    </Button>

                    <Button
                        onClick={() => setActiveFilter('needCare')}
                        variant={activeFilter === 'needCare' ? "primary" : "secondary"}
                        className={`flex items-center gap-2 ${
                            activeFilter === 'needCare'
                                ? 'bg-orange-500 text-white border-orange-500'
                                : 'text-orange-400 border-orange-400 hover:bg-orange-400/10'
                        }`}
                    >
                        <ExclamationTriangleIcon className="w-4 h-4" />
                        Nécessitent des soins
                        <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                            activeFilter === 'needCare' ? 'bg-white/20 text-white' : 'bg-orange-400/10 text-orange-400'
                        }`}>
                            {filterCounts.needCare}
                        </span>
                    </Button>

                    <Button
                        onClick={() => setActiveFilter('archived')}
                        variant={activeFilter === 'archived' ? "primary" : "secondary"}
                        className={`flex items-center gap-2 ${
                            activeFilter === 'archived'
                                ? 'bg-gray-500 text-white border-gray-500'
                                : 'text-gray-400 border-gray-400 hover:bg-gray-400/10'
                        }`}
                    >
                        <ArchiveBoxIcon className="w-4 h-4" />
                        Archivées
                        <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                            activeFilter === 'archived' ? 'bg-white/20 text-white' : 'bg-gray-400/10 text-gray-400'
                        }`}>
                            {filterCounts.archived}
                        </span>
                    </Button>
                </div>

                {/* Ligne 3: Actions de sélection (si mode sélection actif) */}
                {isSelectionMode && selectedPlants.length > 0 && (
                    <div className="mb-6 flex flex-wrap gap-2">
                        <Button
                            onClick={() => handleArchive(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-2 text-[#d385f5] border-[#d385f5] hover:bg-[#d385f5]/10"
                        >
                            <ArchiveBoxIcon className="w-4 h-4" />
                            Archiver ({selectedPlants.length})
                        </Button>

                        <Button
                            onClick={() => handleDuplicate(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-2 text-blue-400 border-blue-400 hover:bg-blue-400/10"
                        >
                            <DocumentDuplicateIcon className="w-4 h-4" />
                            Copier ({selectedPlants.length})
                        </Button>

                        <Button
                            onClick={() => handleMove(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-2 text-yellow-400 border-yellow-400 hover:bg-yellow-400/10"
                        >
                            <ArrowsRightLeftIcon className="w-4 h-4" />
                            Déplacer ({selectedPlants.length})
                        </Button>

                        <Button
                            onClick={() => handleDelete(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-2 text-red-400 border-red-400 hover:bg-red-400/10"
                        >
                            <TrashIcon className="w-4 h-4" />
                            Supprimer ({selectedPlants.length})
                        </Button>
                    </div>
                )}

            <AnimatePresence>
                <motion.div
                    className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
                    initial="hidden"
                    animate="visible"
                    variants={{
                        visible: { transition: { staggerChildren: 0.1 } }
                    }}
                >
                    {plants.map(plant => {
                        const isSelected = selectedPlants.includes(plant.id);
                        return (
                        <Card
                            key={plant.id}
                            onClick={() => handlePlantClick(plant.id)}
                            className={`relative ${isSelectionMode ? 'cursor-pointer' : ''} ${isSelected ? 'ring-2 ring-[#d385f5] bg-[#d385f5]/10' : ''}`}
                        >
                            {/* Indicateur de sélection */}
                            {isSelectionMode && (
                                <div className="absolute top-2 right-2 z-10">
                                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                                        isSelected
                                            ? 'bg-[#d385f5] border-[#d385f5]'
                                            : 'bg-transparent border-gray-400'
                                    }`}>
                                        {isSelected && <CheckIcon className="w-4 h-4 text-white" />}
                                    </div>
                                </div>
                            )}

                            <div className="flex flex-col items-center text-center">
                                {plantThumbnails[plant.id] ? (
                                    <div className="w-20 h-20 mb-4 rounded-full overflow-hidden border-2 border-[#d385f5]/30">
                                        <img
                                            src={plantThumbnails[plant.id]}
                                            alt={plant.name}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                ) : (
                                    <div className="p-3 mb-4 rounded-full bg-gradient-to-r from-[#d385f5]/20 to-[#a364f7]/20">
                                        <img
                                            src={getBackgroundConfig('logo').url}
                                            alt="FloraSynth Logo"
                                            className="w-12 h-12 object-contain"
                                            onError={(e) => {
                                                // Fallback vers l'icône LeafIcon si l'image ne charge pas
                                                const target = e.target as HTMLImageElement;
                                                target.style.display = 'none';
                                                target.nextElementSibling?.classList.remove('hidden');
                                            }}
                                        />
                                        <LeafIcon className="w-12 h-12 text-[#d385f5] hidden" />
                                    </div>
                                )}
                                <h3 className="text-xl font-bold text-white">{plant.name}</h3>
                                {plant.species && <p className="text-sm text-[#E0E0E0]">{plant.species}</p>}
                            </div>
                        </Card>
                        );
                    })}

                    {/* Bouton Ajouter une plante intégré dans la grille */}
                    <motion.div
                        variants={{
                            hidden: { opacity: 0, scale: 0.8 },
                            visible: { opacity: 1, scale: 1 }
                        }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="bg-gradient-to-br from-[#1c1a31]/50 to-[#2a2847]/50 backdrop-blur-lg border-2 border-dashed border-[#d385f5]/50 rounded-2xl p-8 cursor-pointer hover:border-[#d385f5] transition-all duration-300 hover:shadow-lg hover:shadow-[#d385f5]/20 flex flex-col items-center justify-center text-center min-h-[200px]"
                        onClick={() => setIsModalOpen(true)}
                    >
                        <div className="p-4 mb-4 rounded-full bg-gradient-to-r from-[#d385f5]/20 to-[#a364f7]/20">
                            <PlusIcon className="w-12 h-12 text-[#d385f5]" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">Ajouter une Plante</h3>
                        <p className="text-sm text-[#E0E0E0]">Développez votre jardin</p>
                    </motion.div>
                </motion.div>

                {plants.length === 0 && (
                    <motion.div initial={{opacity: 0}} animate={{opacity: 1}} className="text-center py-12 mt-8">
                        <h2 className="text-2xl font-semibold text-white mb-4">Bienvenue dans votre jardin !</h2>
                        <p className="text-[#E0E0E0] mb-6">Commencez par ajouter votre première plante en cliquant sur la carte ci-dessus.</p>
                    </motion.div>
                )}
            </AnimatePresence>


                <AddPlantModal
                    isOpen={isModalOpen}
                    onClose={() => setIsModalOpen(false)}
                    onAdd={handleAddPlant}
                />
            </div>
        </BackgroundWrapper>
    );
};

export default DashboardScreen;