import React from 'react';
import { Button } from '@/components/common/Button';
import {
    LeafIcon,
    HeartIcon,
    ExclamationTriangleIcon,
    ArchiveBoxIcon,
    CheckIcon,
    DocumentDuplicateIcon,
    ArrowsRightLeftIcon,
    TrashIcon
} from '@/components/common/icons';

export type FilterType = 'all' | 'healthy' | 'needCare' | 'archived';

interface PlantFiltersProps {
    activeFilter: FilterType;
    onFilterChange: (filter: FilterType) => void;
    counts: {
        all: number;
        healthy: number;
        needCare: number;
        archived: number;
    };
    // Props pour la gestion de sélection
    selectedPlants: string[];
    totalPlants: number;
    isSelectionMode: boolean;
    onToggleSelectionMode: () => void;
    onSelectAll: () => void;
    onDeselectAll: () => void;
    onArchive: (plantIds: string[]) => void;
    onDuplicate: (plantIds: string[]) => void;
    onMove: (plantIds: string[]) => void;
    onDelete: (plantIds: string[]) => void;
}

const PlantFilters: React.FC<PlantFiltersProps> = ({
    activeFilter,
    onFilterChange,
    counts,
    selectedPlants,
    totalPlants,
    isSelectionMode,
    onToggleSelectionMode,
    onSelectAll,
    onDeselectAll,
    onArchive,
    onDuplicate,
    onMove,
    onDelete
}) => {
    const selectedCount = selectedPlants.length;
    const allSelected = selectedCount === totalPlants && totalPlants > 0;
    const filters = [
        {
            key: 'all' as FilterType,
            label: 'Toutes',
            icon: LeafIcon,
            count: counts.all,
            color: 'text-[#d385f5] border-[#d385f5] hover:bg-[#d385f5]/10'
        },
        {
            key: 'healthy' as FilterType,
            label: 'En bonne santé',
            icon: HeartIcon,
            count: counts.healthy,
            color: 'text-green-400 border-green-400 hover:bg-green-400/10'
        },
        {
            key: 'needCare' as FilterType,
            label: 'Nécessitent des soins',
            icon: ExclamationTriangleIcon,
            count: counts.needCare,
            color: 'text-orange-400 border-orange-400 hover:bg-orange-400/10'
        },
        {
            key: 'archived' as FilterType,
            label: 'Archivées',
            icon: ArchiveBoxIcon,
            count: counts.archived,
            color: 'text-gray-400 border-gray-400 hover:bg-gray-400/10'
        }
    ];

    return (
        <div className="mb-6">
            <div className="flex flex-col gap-4 p-4 bg-[#1c1a31]/60 backdrop-blur-lg rounded-2xl border border-[#3D3B5E]">

                {/* Ligne 1: Filtres */}
                <div className="flex flex-wrap gap-3">
                    {filters.map((filter) => {
                        const Icon = filter.icon;
                        const isActive = activeFilter === filter.key;

                        return (
                            <Button
                                key={filter.key}
                                onClick={() => onFilterChange(filter.key)}
                                variant={isActive ? "primary" : "secondary"}
                                className={`flex items-center gap-2 transition-all duration-200 ${
                                    isActive
                                        ? 'bg-[#d385f5] text-white border-[#d385f5] shadow-lg shadow-[#d385f5]/20'
                                        : filter.color
                                }`}
                            >
                                <Icon className="w-4 h-4" />
                                <span className="font-medium">{filter.label}</span>
                                <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                                    isActive
                                        ? 'bg-white/20 text-white'
                                        : 'bg-current/10 text-current'
                                }`}>
                                    {filter.count}
                                </span>
                            </Button>
                        );
                    })}
                </div>

                {/* Ligne 2: Outils de gestion */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 pt-3 border-t border-[#3D3B5E]">

                    {/* Section gauche : Mode sélection */}
                    <div className="flex items-center gap-3">
                        <Button
                            onClick={onToggleSelectionMode}
                            variant={isSelectionMode ? "primary" : "secondary"}
                            className="flex items-center gap-2"
                        >
                            <CheckIcon className="w-4 h-4" />
                            {isSelectionMode ? 'Quitter sélection' : 'Sélectionner'}
                        </Button>

                        {isSelectionMode && (
                            <>
                                <span className="text-[#E0E0E0] text-sm">
                                    {selectedCount} / {totalPlants} sélectionnée{selectedCount > 1 ? 's' : ''}
                                </span>

                                <Button
                                    onClick={allSelected ? onDeselectAll : onSelectAll}
                                    variant="secondary"
                                    className="text-xs px-3 py-1"
                                >
                                    {allSelected ? 'Tout désélectionner' : 'Tout sélectionner'}
                                </Button>
                            </>
                        )}
                    </div>

                    {/* Section droite : Actions */}
                    {isSelectionMode && selectedCount > 0 && (
                        <div className="flex flex-wrap gap-2">
                            <Button
                                onClick={() => onArchive(selectedPlants)}
                                variant="secondary"
                                className="flex items-center gap-2 text-[#d385f5] border-[#d385f5] hover:bg-[#d385f5]/10"
                            >
                                <ArchiveBoxIcon className="w-4 h-4" />
                                Archiver ({selectedCount})
                            </Button>

                            <Button
                                onClick={() => onDuplicate(selectedPlants)}
                                variant="secondary"
                                className="flex items-center gap-2 text-blue-400 border-blue-400 hover:bg-blue-400/10"
                            >
                                <DocumentDuplicateIcon className="w-4 h-4" />
                                Copier ({selectedCount})
                            </Button>

                            <Button
                                onClick={() => onMove(selectedPlants)}
                                variant="secondary"
                                className="flex items-center gap-2 text-yellow-400 border-yellow-400 hover:bg-yellow-400/10"
                            >
                                <ArrowsRightLeftIcon className="w-4 h-4" />
                                Déplacer ({selectedCount})
                            </Button>

                            <Button
                                onClick={() => onDelete(selectedPlants)}
                                variant="secondary"
                                className="flex items-center gap-2 text-red-400 border-red-400 hover:bg-red-400/10"
                            >
                                <TrashIcon className="w-4 h-4" />
                                Supprimer ({selectedCount})
                            </Button>
                        </div>
                    )}
                </div>
            </div>

            {/* Messages informatifs */}
            <div className="mt-3">
                {/* Message d'aide en mode sélection */}
                {isSelectionMode && selectedCount === 0 && (
                    <div className="p-3 bg-[#2a2847]/50 rounded-lg border border-[#3D3B5E]">
                        <p className="text-[#E0E0E0] text-sm text-center">
                            💡 Cliquez sur les plantes pour les sélectionner, puis choisissez une action
                        </p>
                    </div>
                )}

                {/* Messages selon le filtre actif */}
                {!isSelectionMode && (
                    <>
                        {activeFilter === 'healthy' && counts.healthy === 0 && (
                            <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                                <p className="text-green-400 text-sm text-center">
                                    🌱 Aucune plante en parfaite santé pour le moment
                                </p>
                            </div>
                        )}

                        {activeFilter === 'needCare' && counts.needCare === 0 && (
                            <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                                <p className="text-green-400 text-sm text-center">
                                    ✨ Toutes vos plantes sont en bonne santé !
                                </p>
                            </div>
                        )}

                        {activeFilter === 'archived' && counts.archived === 0 && (
                            <div className="p-3 bg-gray-500/10 border border-gray-500/20 rounded-lg">
                                <p className="text-gray-400 text-sm text-center">
                                    📦 Aucune plante archivée
                                </p>
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

export default PlantFilters;
