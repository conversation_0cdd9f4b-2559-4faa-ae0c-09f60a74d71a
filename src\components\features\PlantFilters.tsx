import React from 'react';
import { But<PERSON> } from '@/components/common/Button';
import { 
    LeafIcon, 
    HeartIcon, 
    ExclamationTriangleIcon, 
    ArchiveBoxIcon 
} from '@/components/common/icons';

export type FilterType = 'all' | 'healthy' | 'needCare' | 'archived';

interface PlantFiltersProps {
    activeFilter: FilterType;
    onFilterChange: (filter: FilterType) => void;
    counts: {
        all: number;
        healthy: number;
        needCare: number;
        archived: number;
    };
}

const PlantFilters: React.FC<PlantFiltersProps> = ({
    activeFilter,
    onFilterChange,
    counts
}) => {
    const filters = [
        {
            key: 'all' as FilterType,
            label: 'Toutes',
            icon: LeafIcon,
            count: counts.all,
            color: 'text-[#d385f5] border-[#d385f5] hover:bg-[#d385f5]/10'
        },
        {
            key: 'healthy' as FilterType,
            label: 'En bonne santé',
            icon: HeartIcon,
            count: counts.healthy,
            color: 'text-green-400 border-green-400 hover:bg-green-400/10'
        },
        {
            key: 'needCare' as FilterType,
            label: 'Nécessitent des soins',
            icon: ExclamationTriangleIcon,
            count: counts.needCare,
            color: 'text-orange-400 border-orange-400 hover:bg-orange-400/10'
        },
        {
            key: 'archived' as FilterType,
            label: 'Archivées',
            icon: ArchiveBoxIcon,
            count: counts.archived,
            color: 'text-gray-400 border-gray-400 hover:bg-gray-400/10'
        }
    ];

    return (
        <div className="mb-6">
            <div className="flex flex-wrap gap-3 p-4 bg-[#1c1a31]/60 backdrop-blur-lg rounded-2xl border border-[#3D3B5E]">
                {filters.map((filter) => {
                    const Icon = filter.icon;
                    const isActive = activeFilter === filter.key;
                    
                    return (
                        <Button
                            key={filter.key}
                            onClick={() => onFilterChange(filter.key)}
                            variant={isActive ? "primary" : "secondary"}
                            className={`flex items-center gap-2 transition-all duration-200 ${
                                isActive 
                                    ? 'bg-[#d385f5] text-white border-[#d385f5] shadow-lg shadow-[#d385f5]/20' 
                                    : filter.color
                            }`}
                        >
                            <Icon className="w-4 h-4" />
                            <span className="font-medium">{filter.label}</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                                isActive 
                                    ? 'bg-white/20 text-white' 
                                    : 'bg-current/10 text-current'
                            }`}>
                                {filter.count}
                            </span>
                        </Button>
                    );
                })}
            </div>
            
            {/* Message informatif selon le filtre actif */}
            <div className="mt-3">
                {activeFilter === 'healthy' && counts.healthy === 0 && (
                    <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                        <p className="text-green-400 text-sm text-center">
                            🌱 Aucune plante en parfaite santé pour le moment
                        </p>
                    </div>
                )}
                
                {activeFilter === 'needCare' && counts.needCare === 0 && (
                    <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                        <p className="text-green-400 text-sm text-center">
                            ✨ Toutes vos plantes sont en bonne santé !
                        </p>
                    </div>
                )}
                
                {activeFilter === 'archived' && counts.archived === 0 && (
                    <div className="p-3 bg-gray-500/10 border border-gray-500/20 rounded-lg">
                        <p className="text-gray-400 text-sm text-center">
                            📦 Aucune plante archivée
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default PlantFilters;
