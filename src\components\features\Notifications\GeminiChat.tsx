import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Loader2 } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { useAuth } from '../../../hooks/useAuth';
import { useDeviceDetection } from '../../../hooks/useDeviceDetection';

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
}

/**
 * Composant de chat avec Gemini IA
 */
export const GeminiChat: React.FC = () => {
  const { user } = useAuth();
  const deviceInfo = useDeviceDetection();
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      content: 'Bonjour ! Je suis votre assistant IA Gemini pour FloraSynth. Je peux vous aider avec vos plantes, répondre à vos questions sur les soins, les maladies, ou tout autre sujet lié au jardinage. Comment puis-je vous aider aujourd\'hui ?',
      isUser: false,
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll vers le bas quand de nouveaux messages arrivent
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputMessage.trim(),
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      // Simulation d'une réponse de Gemini IA
      // Dans une vraie implémentation, ici on ferait appel à l'API Gemini
      await new Promise(resolve => setTimeout(resolve, 1500));

      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: await generateAIResponse(userMessage.content),
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiResponse]);
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: 'Désolé, je rencontre des difficultés techniques. Veuillez réessayer dans quelques instants.',
        isUser: false,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const generateAIResponse = async (userInput: string): Promise<string> => {
    const input = userInput.toLowerCase();

    // Réponses spécifiques pour l'avocat
    if (input.includes('avocat')) {
      if (input.includes('arrosage') || input.includes('arroser')) {
        return 'L\'avocatier nécessite un arrosage modéré. Laissez sécher le sol entre deux arrosages, environ 1-2 fois par semaine en été, moins en hiver. Attention à l\'excès d\'eau qui peut provoquer la pourriture des racines. Vérifiez que le pot a un bon drainage.';
      }
      if (input.includes('feuille') || input.includes('problème') || input.includes('jaune')) {
        return 'Les feuilles d\'avocatier qui jaunissent indiquent souvent un excès d\'arrosage ou un manque de drainage. Si les feuilles brunissent aux extrémités, c\'est généralement dû à l\'air trop sec ou à l\'eau calcaire. Utilisez de l\'eau filtrée et augmentez l\'humidité ambiante.';
      }
      if (input.includes('fruit') || input.includes('production')) {
        return 'Un avocatier d\'intérieur produit rarement des fruits. Il faut généralement 3-7 ans pour qu\'il soit mature, et il nécessite souvent une pollinisation croisée. Concentrez-vous sur sa croissance comme plante décorative d\'intérieur.';
      }
      return 'L\'avocatier est une excellente plante d\'intérieur ! Il aime la lumière vive indirecte, un arrosage modéré, et une température entre 18-24°C. Pincez régulièrement la tige principale pour favoriser la ramification. Que souhaitez-vous savoir spécifiquement ?';
    }

    if (input.includes('arrosage') || input.includes('arroser')) {
      return 'Pour l\'arrosage, la fréquence dépend du type de plante et de la saison. En général, vérifiez l\'humidité du sol en enfonçant votre doigt à 2-3 cm de profondeur. Si c\'est sec, il est temps d\'arroser. Les plantes grasses nécessitent moins d\'eau que les plantes tropicales.';
    }

    if (input.includes('maladie') || input.includes('problème') || input.includes('feuille')) {
      return 'Pour diagnostiquer les problèmes de vos plantes, observez les symptômes : feuilles jaunies (souvent excès d\'eau), taches brunes (champignons), feuilles qui tombent (stress hydrique ou changement d\'environnement). Pouvez-vous me décrire plus précisément ce que vous observez ?';
    }

    if (input.includes('fertilisant') || input.includes('engrais')) {
      return 'La fertilisation dépend de la saison et du type de plante. Au printemps et en été, fertilisez toutes les 2-4 semaines avec un engrais équilibré. En automne et hiver, réduisez ou arrêtez la fertilisation car les plantes sont en dormance.';
    }

    if (input.includes('taille') || input.includes('tailler')) {
      return 'La taille se fait généralement au printemps pour stimuler la croissance. Utilisez des outils propres et désinfectés. Taillez les branches mortes, malades ou qui se croisent. Pour les plantes fleuries, taillez après la floraison.';
    }

    if (input.includes('lumière') || input.includes('exposition')) {
      return 'La plupart des plantes d\'intérieur préfèrent une lumière vive mais indirecte. Évitez le soleil direct qui peut brûler les feuilles. Si votre plante s\'étire vers la lumière, rapprochez-la d\'une fenêtre ou ajoutez un éclairage artificiel.';
    }

    if (input.includes('rempotage') || input.includes('rempoter')) {
      return 'Rempotez quand les racines sortent du pot ou que la croissance ralentit. Choisissez un pot légèrement plus grand avec des trous de drainage. Le meilleur moment est au printemps. Utilisez un terreau adapté au type de plante.';
    }

    // Si aucune réponse spécifique n'est trouvée, essayer une recherche web
    try {
      console.log('🔍 Recherche web pour:', userInput);

      // Simulation d'une recherche web (dans une vraie implémentation, on utiliserait une API de recherche)
      await new Promise(resolve => setTimeout(resolve, 800)); // Simulation du temps de recherche

      return `🌐 J'ai effectué une recherche pour "${userInput}". Voici ce que j'ai trouvé :\n\nPour cette question spécifique, je recommande de consulter des sources spécialisées en jardinage. En attendant, je peux vous donner des conseils généraux sur les soins des plantes.\n\nSi vous avez des questions plus précises sur l'arrosage, la fertilisation, la taille, ou le diagnostic de problèmes, n'hésitez pas à me les poser !`;
    } catch (error) {
      console.error('Erreur lors de la recherche web:', error);
      return 'C\'est une excellente question ! Je peux vous aider avec tous les aspects du soin des plantes : arrosage, fertilisation, taille, diagnostic de maladies, rempotage, exposition, et bien plus. N\'hésitez pas à me poser des questions plus spécifiques sur vos plantes.';
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <Card className={`h-fit flex flex-col ${
      deviceInfo.isMobile || deviceInfo.isTablet
        ? 'max-h-[500px]'
        : 'max-h-[600px]'
    }`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-[#d385f5]">
          <Bot className="h-5 w-5" />
          Chat avec Gemini IA
        </CardTitle>
      </CardHeader>

      <CardContent className="flex flex-col p-0">
        {/* Zone des messages */}
        <div
          className={`overflow-y-scroll p-4 space-y-4 ${
            deviceInfo.isMobile || deviceInfo.isTablet
              ? 'h-64'
              : 'h-80'
          }`}
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.isUser ? 'justify-end' : 'justify-start'}`}
            >
              {!message.isUser && (
                <div className="w-8 h-8 rounded-full bg-[#d385f5] flex items-center justify-center flex-shrink-0">
                  <Bot className="h-4 w-4 text-white" />
                </div>
              )}
              
              <div
                className={`max-w-[80%] p-3 rounded-lg ${
                  message.isUser
                    ? 'bg-[#9333ea] text-white'
                    : 'bg-[#2a2847] text-[#E0E0E0] border border-[#d385f5]/20'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-xs opacity-70 mt-1">
                  {message.timestamp.toLocaleTimeString('fr-FR', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </p>
              </div>
              
              {message.isUser && (
                <div className="w-8 h-8 rounded-full bg-[#2a2847] flex items-center justify-center flex-shrink-0">
                  <User className="h-4 w-4 text-[#d385f5]" />
                </div>
              )}
            </div>
          ))}
          
          {isLoading && (
            <div className="flex gap-3 justify-start">
              <div className="w-8 h-8 rounded-full bg-[#d385f5] flex items-center justify-center flex-shrink-0">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <div className="bg-[#2a2847] text-[#E0E0E0] border border-[#d385f5]/20 p-3 rounded-lg">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">Gemini réfléchit...</span>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
        
        {/* Zone de saisie */}
        <div className={`border-t border-[#d385f5]/20 ${
          deviceInfo.isMobile || deviceInfo.isTablet ? 'p-4 pb-6' : 'p-4'
        }`}>
          <div className="flex gap-2">
            <textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Posez votre question sur vos plantes..."
              className={`flex-1 bg-[#2a2847] border border-[#d385f5]/30 rounded-lg text-[#E0E0E0] placeholder-[#9CA3AF] resize-none focus:outline-none focus:border-[#d385f5] max-h-[100px] ${
                deviceInfo.isMobile || deviceInfo.isTablet
                  ? 'p-3 min-h-[48px] text-base'
                  : 'p-2 min-h-[40px] text-sm'
              }`}
              rows={1}
              disabled={isLoading}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isLoading}
              className={`bg-[#d385f5] hover:bg-[#c070e0] text-white ${
                deviceInfo.isMobile || deviceInfo.isTablet
                  ? 'px-4 py-3 min-h-[48px]'
                  : 'px-3'
              }`}
            >
              <Send className={`${
                deviceInfo.isMobile || deviceInfo.isTablet ? 'h-5 w-5' : 'h-4 w-4'
              }`} />
            </Button>
          </div>
          <p className="text-xs text-[#9CA3AF] mt-2">
            Appuyez sur Entrée pour envoyer, Shift+Entrée pour une nouvelle ligne
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default GeminiChat;
