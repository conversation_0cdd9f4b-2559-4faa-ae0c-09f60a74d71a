import React, { lazy, Suspense, useState } from 'react';
import { HashRouter, Routes, Route, Navigate, Link } from 'react-router-dom';
import { AuthProvider } from '@/context/AuthContext';
import { useAuth } from '@/hooks/useAuth';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { signOutUser } from '@/services/api';
import { Spinner } from '@/components/common/Spinner';
import { ErrorBoundary, AuthErrorBoundary } from '@/components/common/ErrorBoundary';
import { logger } from '@/utils/logger';
import {
  LogoutIcon,
  LeafIcon,
  HomeIcon,
  CalendarIcon,
  BellIcon,
  BookOpenIcon,
  SparklesIcon,
  ArchiveBoxIcon,
  QuestionMarkCircleIcon,
  BeakerIcon
} from '@/components/common/icons';

// Icône Menu Hamburger
const MenuIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
  </svg>
);

// Icône Fermer (X)
const CloseIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);
import { NotificationBadge } from '@/components/features/Notifications/NotificationBadge';
import { usePreventiveNotifications } from '@/hooks/useNotifications';
import { useArchive } from '@/hooks/useArchive';
import { getBackgroundConfig } from '@/utils/backgroundImages';

// Lazy load screen components
const LoginScreen = lazy(() => import('@/components/features/LoginScreen'));
const DashboardScreen = lazy(() => import('@/components/features/DashboardScreen'));
const PlantDetailScreen = lazy(() => import('@/components/features/PlantDetailScreen'));
const CalendarView = lazy(() => import('@/components/features/Calendar/CalendarView'));
const NotificationCenter = lazy(() => import('@/components/features/Notifications/NotificationCenter'));
const GlobalJournal = lazy(() => import('@/components/features/Journal/GlobalJournal'));
const GeminiSettings = lazy(() => import('@/components/features/Notifications/GeminiSettings'));
const ArchiveManager = lazy(() => import('@/components/features/Archive/ArchiveManager'));
const HelpCenter = lazy(() => import('@/components/features/Help/HelpCenter'));
const FertilizerGuideScreen = lazy(() => import('@/components/features/FertilizerGuide/FertilizerGuideScreen'));

const Header: React.FC = () => {
    const { user } = useAuth();
    const deviceInfo = useDeviceDetection();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    // Éléments de navigation
    const navigationItems = [
        { to: "/", icon: HomeIcon, label: "Mes Plantes" },
        { to: "/fertilizer-guide", icon: BeakerIcon, label: "Guide Engrais" },
        { to: "/calendar", icon: CalendarIcon, label: "Calendrier" },
        { to: "/notifications", icon: BellIcon, label: "Notifications" },
        { to: "/journal", icon: BookOpenIcon, label: "Journal" },
        { to: "/gemini-settings", icon: SparklesIcon, label: "Dr. Flora" },
        { to: "/archives", icon: ArchiveBoxIcon, label: "Archives" },
        { to: "/help", icon: QuestionMarkCircleIcon, label: "Aide" }
    ];

    // Fermer le menu mobile lors du clic sur un lien
    const handleMobileLinkClick = () => {
        setIsMobileMenuOpen(false);
    };

    // Menu Desktop (écrans larges)
    const DesktopMenu = () => (
        <nav className="hidden lg:flex items-center gap-4">
            {navigationItems.map(({ to, icon: Icon, label }) => (
                <Link
                    key={to}
                    to={to}
                    className="flex items-center gap-2 text-sm text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-md hover:bg-white/10"
                >
                    <Icon className="w-4 h-4" />
                    {label}
                </Link>
            ))}
        </nav>
    );

    // Menu Mobile/Tablette (menu hamburger)
    const MobileMenu = () => (
        <>
            {/* Bouton hamburger */}
            <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden p-2 rounded-md hover:bg-white/10 transition-colors"
                aria-label="Menu de navigation"
            >
                {isMobileMenuOpen ? (
                    <CloseIcon className="w-6 h-6 text-gray-300" />
                ) : (
                    <MenuIcon className="w-6 h-6 text-gray-300" />
                )}
            </button>

            {/* Menu déroulant mobile */}
            {isMobileMenuOpen && (
                <div className="absolute top-full left-0 right-0 bg-[#1c1a31]/95 backdrop-blur-lg border-t border-gray-600 lg:hidden">
                    <nav className="container mx-auto px-4 py-4 space-y-2">
                        {navigationItems.map(({ to, icon: Icon, label }) => (
                            <Link
                                key={to}
                                to={to}
                                onClick={handleMobileLinkClick}
                                className="flex items-center gap-3 text-gray-300 hover:text-white transition-colors px-4 py-3 rounded-md hover:bg-white/10 text-base"
                            >
                                <Icon className="w-5 h-5" />
                                {label}
                            </Link>
                        ))}

                        {/* Séparateur */}
                        <div className="border-t border-gray-600 my-4"></div>

                        {/* Informations utilisateur et déconnexion */}
                        <div className="px-4 py-2">
                            <p className="text-sm text-gray-400 mb-3">
                                Bienvenue, {user?.displayName || user?.email}
                            </p>
                            <button
                                onClick={() => {
                                    logger.debug('Clic sur le bouton de déconnexion mobile', {
                                        component: 'App',
                                        action: 'MobileLogoutButtonClick'
                                    });
                                    signOutUser();
                                    setIsMobileMenuOpen(false);
                                }}
                                className="flex items-center gap-3 text-red-400 hover:text-red-300 transition-colors px-4 py-3 rounded-md hover:bg-red-500/10 text-base w-full"
                            >
                                <LogoutIcon className="w-5 h-5" />
                                Se déconnecter
                            </button>
                        </div>
                    </nav>
                </div>
            )}
        </>
    );

    return (
        <header className="p-4 bg-[#1c1a31]/50 backdrop-blur-lg sticky top-0 z-50 relative">
            <div className="container mx-auto flex justify-between items-center">
                {/* Logo */}
                <Link to="/" className="flex items-center gap-2" onClick={handleMobileLinkClick}>
                    <img
                        src={getBackgroundConfig('logo').url}
                        alt="FloraSynth Logo"
                        className="w-8 h-8 object-contain"
                        onError={(e) => {
                            // Fallback vers l'icône LeafIcon si l'image ne charge pas
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                        }}
                    />
                    <LeafIcon className="w-8 h-8 text-[#d385f5] hidden" />
                    <span className={`font-bold text-white ${deviceInfo.isMobile ? 'text-xl' : 'text-2xl'}`}>
                        FloraSynth
                    </span>
                </Link>

                {user && (
                    <div className="flex items-center gap-4">
                        {/* Menu Desktop */}
                        <DesktopMenu />

                        {/* Badge de notifications (toujours visible) */}
                        <NotificationBadge />

                        {/* Nom utilisateur (caché sur mobile) */}
                        <span className="text-sm text-gray-300 hidden lg:block">
                            Bienvenue, {user.displayName || user.email}
                        </span>

                        {/* Bouton déconnexion desktop */}
                        <button
                            onClick={() => {
                                logger.debug('Clic sur le bouton de déconnexion desktop', {
                                    component: 'App',
                                    action: 'DesktopLogoutButtonClick'
                                });
                                signOutUser();
                            }}
                            className="hidden lg:block p-2 rounded-full hover:bg-white/10 transition-colors"
                            aria-label="Se déconnecter"
                        >
                           <LogoutIcon className="w-6 h-6 text-gray-300" />
                        </button>

                        {/* Menu Mobile/Tablette */}
                        <MobileMenu />
                    </div>
                )}
            </div>
        </header>
    );
}

const ProtectedLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { user, loading } = useAuth();

    // Activer les notifications préventives pour les utilisateurs connectés
    usePreventiveNotifications();

    // Vérifier et déclencher l'archivage automatique si nécessaire
    const { checkAutoArchive } = useArchive();
    React.useEffect(() => {
        if (user) {
            checkAutoArchive();
        }
    }, [user]); // Seulement dépendant de user pour éviter les boucles infinies

    if (loading) {
        return <div className="flex justify-center items-center h-screen"><Spinner size="lg" /></div>;
    }

    if (!user) {
        return <Navigate to="/login" replace />;
    }

    return (
        <>
            <Header />
            <main>
                {children}
            </main>
        </>
    );
};


const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <AuthErrorBoundary>
        <AuthProvider>
          <HashRouter>
            <Suspense fallback={<div className="flex justify-center items-center h-screen"><Spinner size="lg" /></div>}>
              <Routes>
                <Route path="/login" element={<LoginScreen />} />
                <Route path="/" element={<ProtectedLayout><DashboardScreen /></ProtectedLayout>} />
                <Route path="/plant/:plantId" element={<ProtectedLayout><PlantDetailScreen /></ProtectedLayout>} />
                <Route path="/calendar" element={<ProtectedLayout><CalendarView /></ProtectedLayout>} />
                <Route path="/notifications" element={<ProtectedLayout><NotificationCenter /></ProtectedLayout>} />
                <Route path="/journal" element={<ProtectedLayout><GlobalJournal /></ProtectedLayout>} />
                <Route path="/gemini-settings" element={<ProtectedLayout><GeminiSettings /></ProtectedLayout>} />
                <Route path="/archives" element={<ProtectedLayout><ArchiveManager /></ProtectedLayout>} />
                <Route path="/fertilizer-guide" element={<ProtectedLayout><FertilizerGuideScreen /></ProtectedLayout>} />
                <Route path="/help" element={<ProtectedLayout><HelpCenter /></ProtectedLayout>} />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Suspense>
          </HashRouter>
        </AuthProvider>
      </AuthErrorBoundary>
    </ErrorBoundary>
  );
};

export default App;