/**
 * Utilitaire pour gérer les imports d'images de fond de FloraSynth
 * Centralise tous les imports d'images pour une meilleure gestion
 */

// Import du logo officiel
import logoFloraSynth from '@/assets/images/backgrounds/logo-florasynth.png';

// Import des images de fond pour chaque page
import pageLoginBg from '@/assets/images/backgrounds/page-login.jpg';
import mesPlantesBg from '@/assets/images/backgrounds/Mes_plantes.jpg';
import guideEngraisBg from '@/assets/images/backgrounds/guide-engrais.jpg';
import calendrierBg from '@/assets/images/backgrounds/calendrier.jpg';
import notificationsBg from '@/assets/images/backgrounds/notifications.jpg';
import journalBg from '@/assets/images/backgrounds/journal.jpg';
import parametresGeminiBg from '@/assets/images/backgrounds/parametres-Gemini.jpg';
import archivesBg from '@/assets/images/backgrounds/Archives.jpg';
import pageAideBg from '@/assets/images/backgrounds/page-aide.jpg';
import historiqueDiagnostiquesBg from '@/assets/images/backgrounds/historique-diagnostiques.jpg';

/**
 * Configuration des images de fond pour chaque page
 */
export const BACKGROUND_IMAGES = {
  // Logo officiel
  logo: logoFloraSynth,
  
  // Pages principales
  login: {
    url: pageLoginBg,
    position: 'center' as const,
    size: 'stretch' as const,
    adaptive: true,
    fallbackColor: '#100f1c'
  },
  
  dashboard: {
    url: mesPlantesBg,
    position: 'center' as const,
    size: 'cover' as const, // Cover pour s'adapter sans déformation
    adaptive: true,
    fallbackColor: '#0f0e1a'
  },
  
  fertilizerGuide: {
    url: guideEngraisBg,
    position: 'left' as const, // Positionnée à gauche comme demandé
    size: 'cover' as const,
    adaptive: true,
    fallbackColor: '#0f0e1a'
  },
  
  calendar: {
    url: calendrierBg,
    position: 'center' as const,
    size: 'cover' as const, // Cover pour s'adapter sans déformation
    adaptive: true,
    fallbackColor: '#0f0e1a'
  },
  
  notifications: {
    url: notificationsBg,
    position: 'center' as const,
    size: 'cover' as const,
    adaptive: true,
    fallbackColor: '#0f0e1a'
  },
  
  journal: {
    url: journalBg,
    position: 'center' as const,
    size: 'cover' as const,
    adaptive: true,
    fallbackColor: '#0f0e1a',
    blur: true // Appliquer un flou pour améliorer la lisibilité
  },

  geminiSettings: {
    url: parametresGeminiBg,
    position: 'center' as const,
    size: 'cover' as const,
    adaptive: true,
    fallbackColor: '#0f0e1a',
    blur: true // Appliquer un flou pour améliorer la lisibilité
  },

  archives: {
    url: archivesBg,
    position: 'center' as const,
    size: 'cover' as const,
    adaptive: true,
    fallbackColor: '#0f0e1a'
  },

  help: {
    url: pageAideBg,
    position: 'center' as const,
    size: 'cover' as const,
    adaptive: true,
    fallbackColor: '#0f0e1a'
  },

  historiqueDiagnostiques: {
    url: historiqueDiagnostiquesBg,
    position: 'center' as const,
    size: 'cover' as const,
    adaptive: true,
    fallbackColor: '#0f0e1a'
  }
} as const;

/**
 * Type pour les clés des images de fond disponibles
 */
export type BackgroundImageKey = keyof typeof BACKGROUND_IMAGES;

/**
 * Fonction utilitaire pour obtenir la configuration d'une image de fond
 */
export const getBackgroundConfig = (key: BackgroundImageKey) => {
  const config = BACKGROUND_IMAGES[key];
  
  if (key === 'logo') {
    return { url: config as string };
  }
  
  return {
    imageUrl: (config as any).url,
    position: (config as any).position,
    size: (config as any).size,
    adaptive: (config as any).adaptive,
    fallbackColor: (config as any).fallbackColor
  };
};

/**
 * Fonction pour vérifier si une image est lourde et nécessite une compression
 * (Cette fonction peut être étendue avec une vérification réelle de la taille)
 */
export const isImageHeavy = (key: BackgroundImageKey): boolean => {
  // Images potentiellement lourdes identifiées
  const heavyImages: BackgroundImageKey[] = ['fertilizerGuide', 'dashboard', 'calendar'];
  return heavyImages.includes(key);
};

/**
 * Fonction pour obtenir une version compressée d'une image si nécessaire
 * (Placeholder pour une future implémentation de compression)
 */
export const getOptimizedImageUrl = (key: BackgroundImageKey): string => {
  const config = getBackgroundConfig(key);
  
  if (key === 'logo') {
    return config.url as string;
  }
  
  // Pour l'instant, retourne l'URL originale
  // TODO: Implémenter la compression d'images si nécessaire
  return (config as any).imageUrl;
};

/**
 * Styles CSS communs pour les conteneurs avec image de fond
 */
export const BACKGROUND_CONTAINER_STYLES = {
  // Style de base pour tous les conteneurs
  base: {
    position: 'relative' as const,
    overflow: 'hidden' as const,
    minHeight: '100vh',
    width: '100%'
  },
  
  // Overlay pour améliorer la lisibilité du contenu
  overlay: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    zIndex: 1
  },
  
  // Style pour le contenu au-dessus de l'image
  content: {
    position: 'relative' as const,
    zIndex: 2
  }
} as const;
