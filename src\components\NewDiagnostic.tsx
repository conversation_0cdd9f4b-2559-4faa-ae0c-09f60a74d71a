
import React, { useState, useCallback, useEffect } from 'react';
import { Plant, GeminiDiagnosis } from '@/types';
import { useAuth } from '@/hooks/useAuth';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { uploadImage, analyzePlantImages, addDiagnosticRecord } from '@/services/api';
import { Button } from '@/components/common/Button';
import { Spinner } from '@/components/common/Spinner';
import { CameraIcon, XMarkIcon } from '@/components/common/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { serverTimestamp, Timestamp } from 'firebase/firestore';
import imageCompression from 'browser-image-compression';


interface NewDiagnosticProps {
  plant: Plant;
  onFinish: () => void;
}

type Stage = 'upload' | 'analyzing' | 'result';

export const NewDiagnostic: React.FC<NewDiagnosticProps> = ({ plant, onFinish }) => {
  const { user } = useAuth();
  const deviceInfo = useDeviceDetection();
  const [stage, setStage] = useState<Stage>('upload');
  const [files, setFiles] = useState<File[]>([]);
  const [previews, setPreviews] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<GeminiDiagnosis | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const processFiles = async (fileList: FileList | File[]) => {
    const selectedFiles = Array.from(fileList);

    const options = {
        maxSizeMB: 1,
        maxWidthOrHeight: 1920,
        useWebWorker: true
    }

    try {
        const compressedFiles = await Promise.all(selectedFiles.map(file => imageCompression(file, options)));
        setFiles(prev => [...prev, ...compressedFiles]);

        const newPreviews = compressedFiles.map(file => URL.createObjectURL(file));
        setPreviews(prev => [...prev, ...newPreviews]);

    } catch (error) {
        console.error(error);
        setError("Échec de la compression des images. Veuillez réessayer.");
    }
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
        await processFiles(event.target.files);
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const droppedFiles = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('image/')
    );

    if (droppedFiles.length > 0) {
      await processFiles(droppedFiles);
    } else {
      setError("Veuillez déposer uniquement des fichiers image.");
    }
  }, []);

  const removeImage = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
    setPreviews(previews.filter((_, i) => i !== index));
  };




  
  const handleAnalyze = async () => {
    if (!user || files.length === 0) return;

    setIsLoading(true);
    setStage('analyzing');
    setError(null);

    try {
      const imageUrls = await Promise.all(
        files.map(file => uploadImage(file, `diagnostics/${user.uid}/${plant.id}/${Date.now()}_${file.name}`))
      );

      const base64Images = await Promise.all(
          files.map(file => imageCompression.getDataUrlFromFile(file).then(url => url.split(',')[1]))
      );

      const analysisResult = await analyzePlantImages(base64Images, plant.name);
      
      setResult(analysisResult);
      setStage('result');

    } catch (err) {
      console.error(err);
      setError('Une erreur s\'est produite pendant l\'analyse. Veuillez réessayer.');
      setStage('upload');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveResult = async () => {
      if(!user || !plant || !result) return;
      setIsLoading(true);
      try {
        const imageUrls = await Promise.all(
            files.map(file => uploadImage(file, `diagnostics/${user.uid}/${plant.id}/${Date.now()}_${file.name}`))
        );

        const newRecord = {
            userId: user.uid,
            plantId: plant.id,
            timestamp: serverTimestamp(),
            imageUrls,
            diagnosis: result,
        } as any;

        if(result.treatmentPlan.treatmentFrequencyDays > 0) {
            const nextDate = new Date();
            nextDate.setDate(nextDate.getDate() + result.treatmentPlan.treatmentFrequencyDays);
            newRecord.nextTreatmentDate = Timestamp.fromDate(nextDate);
        }

        await addDiagnosticRecord(user.uid, plant.id, newRecord);
        onFinish();

      } catch (error) {
          console.error("Failed to save result", error);
          setError("Impossible de sauvegarder l'enregistrement du diagnostic.");
      } finally {
          setIsLoading(false);
      }
  };

  const renderContent = () => {
    switch (stage) {
      case 'upload':
        return (
          <>
            <h2 className="text-3xl font-bold text-white mb-2">Nouveau Diagnostic pour {plant.name}</h2>
            <p className="text-[#E0E0E0] mb-4">Téléchargez des photos de votre plante. Pour de meilleurs résultats, incluez une image claire de la zone affectée.</p>

            {/* Message d'alerte pour les utilisateurs PC */}
            {deviceInfo.isDesktop && (
              <div className="mb-6 p-4 bg-gradient-to-r from-orange-500/20 to-yellow-500/20 border border-orange-500/30 rounded-xl">
                <div className="flex items-start gap-3">
                  <div className="text-2xl">📱</div>
                  <div>
                    <h3 className="text-orange-300 font-semibold mb-1">Conseil pour de meilleurs résultats</h3>
                    <p className="text-orange-100 text-sm leading-relaxed">
                      Vous visitez l'application sur PC. Pour une expérience optimale et des photos de meilleure qualité,
                      nous vous recommandons d'utiliser votre <strong>smartphone</strong> pour prendre les photos directement sur votre plante.
                      L'appareil photo du smartphone offre une meilleure qualité et une expérience plus intuitive pour le diagnostic.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Message de confirmation pour les utilisateurs mobile */}
            {deviceInfo.isMobile && (
              <div className="mb-6 p-4 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-xl">
                <div className="flex items-start gap-3">
                  <div className="text-2xl">✅</div>
                  <div>
                    <h3 className="text-green-300 font-semibold mb-1">Parfait !</h3>
                    <p className="text-green-100 text-sm leading-relaxed">
                      Vous utilisez l'application sur smartphone ! C'est l'appareil idéal pour prendre des photos de qualité
                      et obtenir les meilleurs diagnostics pour vos plantes.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Zone d'upload adaptative selon l'appareil */}
            <div
              className={`p-8 border-2 border-dashed rounded-2xl text-center transition-all duration-200 ${
                isDragOver
                  ? 'border-[#d385f5] bg-[#d385f5]/10'
                  : 'border-gray-600 bg-[#100f1c]'
              }`}
              onDragOver={deviceInfo.isDesktop ? handleDragOver : undefined}
              onDragLeave={deviceInfo.isDesktop ? handleDragLeave : undefined}
              onDrop={deviceInfo.isDesktop ? handleDrop : undefined}
            >
              <input
                type="file"
                id="file-upload"
                className="hidden"
                multiple
                accept="image/*"
                capture={deviceInfo.isMobile ? "environment" : undefined}
                onChange={handleFileChange}
              />
              <label htmlFor="file-upload" className="cursor-pointer">
                <CameraIcon className={`w-12 h-12 mx-auto mb-4 transition-colors duration-200 ${
                  isDragOver ? 'text-[#d385f5]' : 'text-gray-400'
                }`} />

                {/* Interface adaptée selon l'appareil */}
                {deviceInfo.isMobile ? (
                  <>
                    <p className={`font-semibold transition-colors duration-200 ${
                      isDragOver ? 'text-[#d385f5]' : 'text-white'
                    }`}>
                      📷 Prendre une photo ou choisir dans la galerie
                    </p>
                    <p className={`text-sm transition-colors duration-200 ${
                      isDragOver ? 'text-[#d385f5]/80' : 'text-gray-400'
                    }`}>
                      Appuyez pour accéder à votre appareil photo ou galerie
                    </p>
                  </>
                ) : deviceInfo.isTablet ? (
                  <>
                    <p className={`font-semibold transition-colors duration-200 ${
                      isDragOver ? 'text-[#d385f5]' : 'text-white'
                    }`}>
                      {isDragOver ? 'Déposez vos images ici' : '📁 Choisir des fichiers ou prendre une photo'}
                    </p>
                    <p className={`text-sm transition-colors duration-200 ${
                      isDragOver ? 'text-[#d385f5]/80' : 'text-gray-400'
                    }`}>
                      Appuyez pour sélectionner ou glissez-déposez vos images
                    </p>
                  </>
                ) : (
                  <>
                    <p className={`font-semibold transition-colors duration-200 ${
                      isDragOver ? 'text-[#d385f5]' : 'text-white'
                    }`}>
                      {isDragOver ? 'Déposez vos images ici' : '📁 Cliquez pour sélectionner des fichiers'}
                    </p>
                    <p className={`text-sm transition-colors duration-200 ${
                      isDragOver ? 'text-[#d385f5]/80' : 'text-gray-400'
                    }`}>
                      ou glissez-déposez vos images depuis votre ordinateur
                    </p>
                  </>
                )}
              </label>

              {/* Informations sur l'appareil (debug) */}
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-4 text-xs text-gray-500 border-t border-gray-700 pt-2">
                  Appareil détecté: {deviceInfo.type} ({deviceInfo.screenWidth}x{deviceInfo.screenHeight})
                  {deviceInfo.hasCamera && ' • Caméra disponible'}
                  {deviceInfo.hasTouchScreen && ' • Écran tactile'}
                </div>
              )}
            </div>

            {/* Aperçu des images sélectionnées */}
            <AnimatePresence>
                {previews.length > 0 && (
                    <div className="mt-6">
                      <h3 className="text-white font-semibold mb-4">Images sélectionnées ({files.length})</h3>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                        {previews.map((src, index) => (
                            <motion.div key={index} layout className="relative">
                                <img src={src} alt={`preview ${index}`} className="rounded-lg w-full h-32 object-cover"/>
                                <button onClick={() => removeImage(index)} className="absolute top-1 right-1 bg-black/50 rounded-full p-1 text-white">
                                    <XMarkIcon className="w-4 h-4" />
                                </button>
                            </motion.div>
                        ))}
                      </div>
                    </div>
                )}
            </AnimatePresence>

            <div className="mt-8 flex justify-end gap-4">
              <Button variant="secondary" onClick={onFinish}>Annuler</Button>
              <Button onClick={handleAnalyze} disabled={files.length === 0} isLoading={isLoading}>Analyser les Images</Button>
            </div>
          </>
        );
      case 'analyzing':
        return (
            <div className="text-center">
                <Spinner size="lg" />
                <h2 className="text-3xl font-bold text-white mt-6">Analyse en cours...</h2>
                <p className="text-[#E0E0E0] mt-2">FloraSynth inspecte votre plante. Cela peut prendre un moment.</p>
            </div>
        );
      case 'result':
        return result && (
            <div>
                <h2 className="text-3xl font-bold text-white mb-2">Analyse Terminée</h2>
                <p className="text-xl text-white mb-4">Diagnostic : <span className="font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#d385f5] to-[#a364f7]">{result.disease}</span></p>
                <div className="space-y-6 text-[#E0E0E0] p-6 bg-[#100f1c] rounded-lg">
                    <p>{result.description}</p>

                    <div>
                        <h3 className="font-bold text-white text-xl mb-4 flex items-center gap-2">
                            📋 Plan de Traitement
                        </h3>
                        <div className="space-y-3 mb-6">
                            {result.treatmentPlan.steps.map((step, i) => (
                                <div key={i} className="flex items-start gap-3 p-4 bg-[#2a2847] rounded-lg border border-[#3D3B5E]">
                                    <div className="flex-shrink-0 w-7 h-7 bg-[#d385f5] text-white rounded-full flex items-center justify-center text-sm font-bold">
                                        {i + 1}
                                    </div>
                                    <p className="text-[#E0E0E0] leading-relaxed">{step}</p>
                                </div>
                            ))}
                        </div>
                        {result.treatmentPlan.treatmentFrequencyDays > 0 && (
                            <div className="bg-gradient-to-r from-[#d385f5]/20 to-[#a364f7]/20 p-4 rounded-lg border border-[#d385f5]/30 mb-6">
                                <div className="flex items-center gap-2">
                                    <span className="text-2xl">🔄</span>
                                    <p className="font-semibold text-[#d385f5] text-lg">
                                        Répéter le traitement tous les {result.treatmentPlan.treatmentFrequencyDays} jours
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>

                    {result.treatmentPlan.recommendedProducts.length > 0 && (
                        <div>
                            <h3 className="font-bold text-white text-xl mb-4 flex items-center gap-2">
                                🧪 Produits Recommandés et Dosages
                            </h3>
                            <div className="space-y-6">
                                {result.treatmentPlan.recommendedProducts.map((product, i) => (
                                    <div key={i} className="bg-[#2a2847] p-6 rounded-xl border border-[#3D3B5E] shadow-lg">
                                        <div className="flex items-center gap-3 mb-4">
                                            <div className="w-10 h-10 bg-gradient-to-br from-[#d385f5] to-[#a364f7] rounded-full flex items-center justify-center">
                                                <span className="text-white font-bold text-lg">🧪</span>
                                            </div>
                                            <div>
                                                <h4 className="font-bold text-[#d385f5] text-xl">{product.name}</h4>
                                                <p className="text-sm text-[#9CA3AF]">{product.type}</p>
                                            </div>
                                        </div>

                                        {product.dosages && (
                                            <div className="mb-6">
                                                <h5 className="font-bold text-white text-lg mb-4 flex items-center gap-2">
                                                    ⚖️ Dosages par Récipient
                                                </h5>
                                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                                    {/* Pulvérisateurs */}
                                                    <div className="space-y-3">
                                                        <h6 className="font-semibold text-[#d385f5] text-sm uppercase tracking-wide flex items-center gap-2">
                                                            💨 Pulvérisateurs
                                                        </h6>
                                                        <div className="space-y-2">
                                                            <div className="flex items-center justify-between p-3 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                                <div className="flex items-center gap-2">
                                                                    <div className="w-8 h-8 bg-[#d385f5]/20 rounded-lg flex items-center justify-center">
                                                                        <span className="text-[#d385f5] font-bold text-sm">P</span>
                                                                    </div>
                                                                    <span className="text-[#E0E0E0] font-medium">Pulvérisateur 1L</span>
                                                                </div>
                                                                <span className="font-bold text-[#d385f5] text-lg">{product.dosages.pulverisateur_1L}</span>
                                                            </div>
                                                            <div className="flex items-center justify-between p-3 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                                <div className="flex items-center gap-2">
                                                                    <div className="w-8 h-8 bg-[#d385f5]/20 rounded-lg flex items-center justify-center">
                                                                        <span className="text-[#d385f5] font-bold text-sm">P</span>
                                                                    </div>
                                                                    <span className="text-[#E0E0E0] font-medium">Pulvérisateur 5L</span>
                                                                </div>
                                                                <span className="font-bold text-[#d385f5] text-lg">{product.dosages.pulverisateur_5L}</span>
                                                            </div>
                                                            <div className="flex items-center justify-between p-3 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                                <div className="flex items-center gap-2">
                                                                    <div className="w-8 h-8 bg-[#d385f5]/20 rounded-lg flex items-center justify-center">
                                                                        <span className="text-[#d385f5] font-bold text-sm">P</span>
                                                                    </div>
                                                                    <span className="text-[#E0E0E0] font-medium">Pulvérisateur 16L</span>
                                                                </div>
                                                                <span className="font-bold text-[#d385f5] text-lg">{product.dosages.pulverisateur_16L}</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    {/* Arrosoirs */}
                                                    <div className="space-y-3">
                                                        <h6 className="font-semibold text-[#10B981] text-sm uppercase tracking-wide flex items-center gap-2">
                                                            🪣 Arrosoirs
                                                        </h6>
                                                        <div className="space-y-2">
                                                            <div className="flex items-center justify-between p-3 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                                <div className="flex items-center gap-2">
                                                                    <div className="w-8 h-8 bg-[#10B981]/20 rounded-lg flex items-center justify-center">
                                                                        <span className="text-[#10B981] font-bold text-sm">A</span>
                                                                    </div>
                                                                    <span className="text-[#E0E0E0] font-medium">Arrosoir 11L</span>
                                                                </div>
                                                                <span className="font-bold text-[#10B981] text-lg">{product.dosages.arrosoir_11L}</span>
                                                            </div>
                                                            <div className="flex items-center justify-between p-3 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                                <div className="flex items-center gap-2">
                                                                    <div className="w-8 h-8 bg-[#10B981]/20 rounded-lg flex items-center justify-center">
                                                                        <span className="text-[#10B981] font-bold text-sm">A</span>
                                                                    </div>
                                                                    <span className="text-[#E0E0E0] font-medium">Arrosoir 13L</span>
                                                                </div>
                                                                <span className="font-bold text-[#10B981] text-lg">{product.dosages.arrosoir_13L}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        <div className="mb-4">
                                            <h5 className="font-bold text-white text-lg mb-3 flex items-center gap-2">
                                                🎯 Méthode d'Application
                                            </h5>
                                            <div className="p-4 bg-[#1c1a31] rounded-lg border border-[#3D3B5E]">
                                                <p className="text-[#E0E0E0] leading-relaxed">{product.applicationMethod}</p>
                                            </div>
                                        </div>

                                        {product.precautions && (
                                            <div className="p-4 bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30 rounded-lg">
                                                <h5 className="font-bold text-orange-400 text-lg mb-2 flex items-center gap-2">
                                                    ⚠️ Précautions Importantes
                                                </h5>
                                                <p className="text-orange-200 leading-relaxed">{product.precautions}</p>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    <div>
                        <h3 className="font-bold text-white text-xl mb-4 flex items-center gap-2">
                            💡 Conseils de Soin Généraux
                        </h3>
                        <div className="space-y-3">
                            {result.careTips.map((tip, i) => (
                                <div key={i} className="flex items-start gap-3 p-4 bg-[#2a2847] rounded-lg border border-[#3D3B5E]">
                                    <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-br from-[#10B981] to-[#059669] text-white rounded-full flex items-center justify-center text-sm">
                                        💡
                                    </div>
                                    <p className="text-[#E0E0E0] leading-relaxed">{tip}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
                 <div className="mt-8 flex justify-end gap-4">
                    <Button variant="secondary" onClick={() => setStage('upload')}>Re-télécharger</Button>
                    <Button onClick={handleSaveResult} isLoading={isLoading}>Sauvegarder dans l'Historique</Button>
                </div>
            </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-[#100f1c]/80 backdrop-blur-sm">
      <motion.div 
        initial={{opacity: 0, scale: 0.95}}
        animate={{opacity: 1, scale: 1}}
        className="w-full max-w-3xl bg-[#1c1a31] p-8 rounded-2xl"
      >
        {error && <p className="text-red-400 mb-4 text-center">{error}</p>}
        {renderContent()}
      </motion.div>
    </div>
  );
};
